'use client';

import { DirectoryEntityActions } from '@/app/[locale]/bottin/directory-page/directory-entity-actions';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import type { SupportedLocale } from '@/types/locale';
import type { BuildingList } from '@rie/domain/types';
import type { ColumnDef } from '@tanstack/react-table';
import { useFormatter } from 'next-intl';

export const buildingsColumns = (
  _locale: SupportedLocale,
): ColumnDef<BuildingList>[] => {
  const directoryEntity = 'building' as const;

  return [
    {
      accessorKey: 'name',
      cell: (cell) => {
        const name = cell.getValue() as string | null;
        return name || '-';
      },
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.name"
        />
      ),
      id: 'name',
      maxSize: 400,
      minSize: 250,
    },
    {
      accessorKey: 'campus',
      cell: (cell) => {
        const campus = cell.getValue() as {
          id: string;
          text: string;
        } | null;
        return campus?.text || '-';
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.campus"
        />
      ),
      id: 'campus',
      maxSize: 150,
    },
    {
      accessorKey: 'otherNames',
      cell: (cell) => {
        const names = cell.getValue() as string[];
        return names && names.length > 0 ? names.join(', ') : '-';
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.otherNames"
        />
      ),
      id: 'otherNames',
      maxSize: 200,
    },
    {
      accessorKey: 'updatedAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'lastUpdatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => (
        <DirectoryEntityActions row={row} directoryEntity={directoryEntity} />
      ),
      id: 'actions',
      size: 105,
    },
  ];
};
