'use client';

import { buildingsColumns } from '@/app/[locale]/bottin/batiments/columns';
import { GenericList } from '@/components/bottin-table/generic-list';
import { initialColumnVisibility } from '@/constants/buildings';
import type { SupportedLocale } from '@/types/locale';

// Type qui correspond à ce que votre backend retourne réellement
type BuildingWithTranslations = {
  id: string;
  civicAddressId: string | null;
  sadId: string | null;
  diId: string | null;
  createdAt: string;
  updatedAt: string;
  modifiedBy: string | null;
  translations: Array<{
    locale: string;
    name: string | null;
  }>;
  campus: {
    id: string;
    translations: Array<{ locale: string; name: string | null }>;
  } | null;
  otherNames: string[];
  text: string; // Pour DirectoryEntityActions
};

type BuildingsListProps = {
  locale: SupportedLocale;
};

export const BuildingsList = ({ locale }: BuildingsListProps) => {
  return (
    <GenericList<
      'building',
      'list',
      BuildingWithTranslations,
      Record<string, unknown>
    >
      controlledListKey="building"
      view="list"
      locale={locale}
      columns={buildingsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="buildings"
    />
  );
};
